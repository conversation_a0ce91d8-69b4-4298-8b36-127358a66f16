import React, { useState, useEffect, useCallback } from 'react';
import { SecurityValidatorAgent, SecurityValidationRequest, SecurityValidationResult, SecurityVulnerability } from '../security/security_validator_agent';
import { SecurityAgent } from '../../agents/security/src/core/SecurityAgent';
import { VulnerabilityScanner } from '../security/vulnerability_scanner';
import { SecurityPolicies } from '../security/security_policies';

// Types pour l'interface de validation sécurité
export interface SecurityDashboardStats {
  totalValidations: number;
  pendingValidations: number;
  completedValidations: number;
  blockedValidations: number;
  averageScore: number;
  criticalVulnerabilities: number;
  highVulnerabilities: number;
  mediumVulnerabilities: number;
  lowVulnerabilities: number;
  complianceRate: number;
}

export interface SecurityValidationFilter {
  status?: 'pending' | 'scanning' | 'completed' | 'failed' | 'blocked';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  type?: 'code' | 'container' | 'environment' | 'deployment';
  dateRange?: {
    start: Date;
    end: Date;
  };
  scoreRange?: {
    min: number;
    max: number;
  };
}

export interface SecurityAlert {
  id: string;
  type: 'vulnerability' | 'policy-violation' | 'compliance-failure' | 'security-incident';
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  timestamp: Date;
  acknowledged: boolean;
  resolved: boolean;
  validationId?: string;
}

interface SecurityValidationInterfaceProps {
  securityAgent: SecurityAgent;
  vulnerabilityScanner: VulnerabilityScanner;
  securityPolicies: SecurityPolicies;
  onError?: (error: Error) => void;
}

/**
 * Interface de Validation Sécurité pour la Sandbox Hanuman
 * Dashboard central pour la gestion et le monitoring des validations de sécurité
 */
export const SecurityValidationInterface: React.FC<SecurityValidationInterfaceProps> = ({
  securityAgent,
  vulnerabilityScanner,
  securityPolicies,
  onError
}) => {
  // États principaux
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'validations' | 'vulnerabilities' | 'policies' | 'reports' | 'alerts'>('dashboard');

  // Données de sécurité
  const [dashboardStats, setDashboardStats] = useState<SecurityDashboardStats>({
    totalValidations: 0,
    pendingValidations: 0,
    completedValidations: 0,
    blockedValidations: 0,
    averageScore: 0,
    criticalVulnerabilities: 0,
    highVulnerabilities: 0,
    mediumVulnerabilities: 0,
    lowVulnerabilities: 0,
    complianceRate: 0
  });

  const [validationRequests, setValidationRequests] = useState<SecurityValidationRequest[]>([]);
  const [validationResults, setValidationResults] = useState<SecurityValidationResult[]>([]);
  const [vulnerabilities, setVulnerabilities] = useState<SecurityVulnerability[]>([]);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);

  // Filtres et recherche
  const [filter, setFilter] = useState<SecurityValidationFilter>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Agent validateur
  const [validatorAgent, setValidatorAgent] = useState<SecurityValidatorAgent | null>(null);

  // Initialisation
  useEffect(() => {
    initializeInterface();
  }, []);

  /**
   * Initialise l'interface de validation sécurité
   */
  const initializeInterface = useCallback(async () => {
    try {
      console.log('🛡️ Initialisation de l\'Interface de Validation Sécurité...');
      setIsLoading(true);

      // Initialiser les composants de sécurité
      await Promise.all([
        securityAgent.initialize(),
        vulnerabilityScanner.initialize(),
        securityPolicies.initialize()
      ]);

      // L'agent validateur sera initialisé séparément
      // Pour l'instant, on simule son existence
      setValidatorAgent(null);

      // Charger les données initiales
      await loadInitialData();

      // Configurer les écouteurs d'événements
      setupEventListeners();

      setIsInitialized(true);
      setIsLoading(false);
      console.log('✅ Interface de Validation Sécurité initialisée');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation:', error);
      setIsLoading(false);
      onError?.(error as Error);
    }
  }, [securityAgent, vulnerabilityScanner, securityPolicies, onError]);

  /**
   * Charge les données initiales
   */
  const loadInitialData = useCallback(async () => {
    try {
      // Charger les validations récentes
      const recentValidations = await loadRecentValidations();
      setValidationRequests(recentValidations.requests);
      setValidationResults(recentValidations.results);

      // Charger les vulnérabilités actives
      const activeVulnerabilities = await loadActiveVulnerabilities();
      setVulnerabilities(activeVulnerabilities);

      // Charger les alertes de sécurité
      const alerts = await loadSecurityAlerts();
      setSecurityAlerts(alerts);

      // Calculer les statistiques du dashboard
      const stats = calculateDashboardStats(recentValidations.results, activeVulnerabilities);
      setDashboardStats(stats);

    } catch (error) {
      console.error('❌ Erreur lors du chargement des données:', error);
      throw error;
    }
  }, []);

  /**
   * Configure les écouteurs d'événements
   */
  const setupEventListeners = useCallback(() => {
    // Écouter les nouvelles validations
    securityAgent.on('validation:requested', handleNewValidationRequest);

    // Écouter les résultats de scan
    vulnerabilityScanner.on('scan:completed', handleScanCompleted);

    // Écouter les violations de politique
    securityPolicies.on('policy:violation', handlePolicyViolation);

    // Écouter les incidents de sécurité
    securityAgent.on('security:incident', handleSecurityIncident);

  }, []);

  /**
   * Traite une nouvelle demande de validation
   */
  const handleNewValidationRequest = useCallback((request: SecurityValidationRequest) => {
    setValidationRequests(prev => [request, ...prev]);
    updateDashboardStats();

    // Créer une alerte pour les demandes critiques
    if (request.priority === 'critical') {
      const alert: SecurityAlert = {
        id: `alert_${Date.now()}`,
        type: 'security-incident',
        severity: 'critical',
        title: 'Validation Critique Demandée',
        description: `Validation critique demandée pour ${request.type}: ${request.target.agentId || request.target.containerId}`,
        timestamp: new Date(),
        acknowledged: false,
        resolved: false,
        validationId: request.id
      };
      setSecurityAlerts(prev => [alert, ...prev]);
    }
  }, []);

  /**
   * Traite la completion d'une validation
   */
  const handleValidationComplete = useCallback((result: SecurityValidationResult) => {
    setValidationResults(prev => {
      const updated = prev.filter(r => r.id !== result.id);
      return [result, ...updated];
    });

    // Mettre à jour les vulnérabilités
    if (result.vulnerabilities.length > 0) {
      setVulnerabilities(prev => [...result.vulnerabilities, ...prev]);
    }

    updateDashboardStats();

    // Créer des alertes pour les résultats critiques
    if (!result.approved || result.score < 50) {
      const alert: SecurityAlert = {
        id: `alert_${Date.now()}`,
        type: result.approved ? 'vulnerability' : 'compliance-failure',
        severity: result.score < 30 ? 'critical' : result.score < 50 ? 'high' : 'medium',
        title: result.approved ? 'Vulnérabilités Détectées' : 'Validation Bloquée',
        description: `Score de sécurité: ${result.score}%. ${result.vulnerabilities.length} vulnérabilités trouvées.`,
        timestamp: new Date(),
        acknowledged: false,
        resolved: false,
        validationId: result.id
      };
      setSecurityAlerts(prev => [alert, ...prev]);
    }
  }, []);

  /**
   * Traite la completion d'un scan
   */
  const handleScanCompleted = useCallback((scanResult: any) => {
    console.log('🔍 Scan terminé:', scanResult);
    // Recharger les données si nécessaire
    loadInitialData();
  }, [loadInitialData]);

  /**
   * Traite une violation de politique
   */
  const handlePolicyViolation = useCallback((violation: any) => {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}`,
      type: 'policy-violation',
      severity: violation.severity,
      title: 'Violation de Politique',
      description: violation.description,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false
    };
    setSecurityAlerts(prev => [alert, ...prev]);
  }, []);

  /**
   * Traite un incident de sécurité
   */
  const handleSecurityIncident = useCallback((incident: any) => {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}`,
      type: 'security-incident',
      severity: incident.severity,
      title: 'Incident de Sécurité',
      description: incident.description,
      timestamp: new Date(),
      acknowledged: false,
      resolved: false
    };
    setSecurityAlerts(prev => [alert, ...prev]);
  }, []);

  /**
   * Charge les validations récentes
   */
  const loadRecentValidations = useCallback(async () => {
    try {
      // Simuler le chargement des validations récentes
      const mockRequests: SecurityValidationRequest[] = [
        {
          id: 'val_001',
          type: 'code',
          target: { agentId: 'agent_frontend', codeRepository: 'frontend-repo' },
          priority: 'high',
          requestedBy: 'agent_devops',
          timestamp: new Date(Date.now() - 3600000),
          metadata: { branch: 'feature/security-update' }
        },
        {
          id: 'val_002',
          type: 'container',
          target: { containerId: 'container_backend' },
          priority: 'medium',
          requestedBy: 'agent_security',
          timestamp: new Date(Date.now() - 7200000)
        }
      ];

      const mockResults: SecurityValidationResult[] = [
        {
          id: 'result_001',
          requestId: 'val_001',
          status: 'completed',
          score: 85,
          vulnerabilities: [],
          complianceResults: [],
          recommendations: [],
          approved: true,
          blockers: [],
          timestamp: new Date(),
          scanDuration: 45000
        }
      ];

      return { requests: mockRequests, results: mockResults };
    } catch (error) {
      console.error('Erreur lors du chargement des validations:', error);
      return { requests: [], results: [] };
    }
  }, []);

  /**
   * Charge les vulnérabilités actives
   */
  const loadActiveVulnerabilities = useCallback(async (): Promise<SecurityVulnerability[]> => {
    try {
      // Simuler le chargement des vulnérabilités
      const mockVulnerabilities: SecurityVulnerability[] = [
        {
          id: 'vuln_001',
          type: 'code',
          severity: 'high',
          title: 'Injection SQL potentielle',
          description: 'Paramètre non sanitisé dans la requête de base de données',
          location: 'src/api/user.controller.ts:45',
          cve: 'CVE-2023-1234',
          cvss: 7.5,
          remediation: 'Utiliser des requêtes préparées ou un ORM sécurisé',
          status: 'open'
        },
        {
          id: 'vuln_002',
          type: 'dependency',
          severity: 'medium',
          title: 'Dépendance obsolète',
          description: 'Version obsolète de lodash avec vulnérabilités connues',
          location: 'package.json',
          cve: 'CVE-2023-5678',
          cvss: 5.3,
          remediation: 'Mettre à jour lodash vers la version 4.17.21 ou supérieure',
          status: 'open'
        }
      ];

      return mockVulnerabilities;
    } catch (error) {
      console.error('Erreur lors du chargement des vulnérabilités:', error);
      return [];
    }
  }, []);

  /**
   * Charge les alertes de sécurité
   */
  const loadSecurityAlerts = useCallback(async (): Promise<SecurityAlert[]> => {
    try {
      const mockAlerts: SecurityAlert[] = [
        {
          id: 'alert_001',
          type: 'vulnerability',
          severity: 'high',
          title: 'Vulnérabilité critique détectée',
          description: 'Injection SQL détectée dans le module d\'authentification',
          timestamp: new Date(Date.now() - 1800000),
          acknowledged: false,
          resolved: false,
          validationId: 'val_001'
        },
        {
          id: 'alert_002',
          type: 'policy-violation',
          severity: 'medium',
          title: 'Violation de politique de sécurité',
          description: 'Mot de passe faible détecté dans la configuration',
          timestamp: new Date(Date.now() - 3600000),
          acknowledged: true,
          resolved: false
        }
      ];

      return mockAlerts;
    } catch (error) {
      console.error('Erreur lors du chargement des alertes:', error);
      return [];
    }
  }, []);

  /**
   * Calcule les statistiques du dashboard
   */
  const calculateDashboardStats = useCallback((results: SecurityValidationResult[], vulnerabilities: SecurityVulnerability[]): SecurityDashboardStats => {
    const totalValidations = results.length;
    const pendingValidations = results.filter(r => r.status === 'pending' || r.status === 'scanning').length;
    const completedValidations = results.filter(r => r.status === 'completed').length;
    const blockedValidations = results.filter(r => r.status === 'blocked').length;

    const averageScore = totalValidations > 0
      ? Math.round(results.reduce((sum, r) => sum + r.score, 0) / totalValidations)
      : 0;

    const criticalVulnerabilities = vulnerabilities.filter(v => v.severity === 'critical').length;
    const highVulnerabilities = vulnerabilities.filter(v => v.severity === 'high').length;
    const mediumVulnerabilities = vulnerabilities.filter(v => v.severity === 'medium').length;
    const lowVulnerabilities = vulnerabilities.filter(v => v.severity === 'low').length;

    const approvedValidations = results.filter(r => r.approved).length;
    const complianceRate = totalValidations > 0
      ? Math.round((approvedValidations / totalValidations) * 100)
      : 100;

    return {
      totalValidations,
      pendingValidations,
      completedValidations,
      blockedValidations,
      averageScore,
      criticalVulnerabilities,
      highVulnerabilities,
      mediumVulnerabilities,
      lowVulnerabilities,
      complianceRate
    };
  }, []);

  /**
   * Met à jour les statistiques du dashboard
   */
  const updateDashboardStats = useCallback(() => {
    const stats = calculateDashboardStats(validationResults, vulnerabilities);
    setDashboardStats(stats);
  }, [validationResults, vulnerabilities, calculateDashboardStats]);

  /**
   * Démarre une nouvelle validation
   */
  const startNewValidation = useCallback(async (request: Omit<SecurityValidationRequest, 'id' | 'timestamp'>) => {
    try {
      const newRequest: SecurityValidationRequest = {
        ...request,
        id: `val_${Date.now()}`,
        timestamp: new Date()
      };

      if (validatorAgent) {
        await validatorAgent.requestValidation(newRequest);
      }
    } catch (error) {
      console.error('Erreur lors du démarrage de la validation:', error);
      onError?.(error as Error);
    }
  }, [validatorAgent, onError]);

  /**
   * Acquitte une alerte
   */
  const acknowledgeAlert = useCallback((alertId: string) => {
    setSecurityAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId
          ? { ...alert, acknowledged: true }
          : alert
      )
    );
  }, []);

  /**
   * Résout une alerte
   */
  const resolveAlert = useCallback((alertId: string) => {
    setSecurityAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId
          ? { ...alert, resolved: true, acknowledged: true }
          : alert
      )
    );
  }, []);

  /**
   * Filtre les validations selon les critères
   */
  const filteredValidations = useCallback(() => {
    let filtered = validationResults;

    if (filter.status) {
      filtered = filtered.filter(v => v.status === filter.status);
    }

    if (filter.scoreRange) {
      filtered = filtered.filter(v =>
        v.score >= filter.scoreRange!.min && v.score <= filter.scoreRange!.max
      );
    }

    if (searchTerm) {
      filtered = filtered.filter(v =>
        v.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        v.requestId.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [validationResults, filter, searchTerm]);

  /**
   * Filtre les vulnérabilités selon les critères
   */
  const filteredVulnerabilities = useCallback(() => {
    let filtered = vulnerabilities;

    if (searchTerm) {
      filtered = filtered.filter(v =>
        v.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        v.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        v.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [vulnerabilities, searchTerm]);

  return (
    <div className="security-validation-interface min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                🛡️ Interface de Validation Sécurité
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Validation et monitoring de sécurité pour la Sandbox Hanuman
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                dashboardStats.averageScore >= 80 ? 'bg-green-100 text-green-800' :
                dashboardStats.averageScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                Score: {dashboardStats.averageScore}%
              </div>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  isInitialized ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm text-gray-600">
                  {isInitialized ? 'Actif' : 'Inactif'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-4 text-lg text-gray-600">Initialisation de l'interface de sécurité...</p>
            <p className="text-sm text-gray-500">Chargement des composants de validation</p>
          </div>
        </div>
      ) : (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Navigation par onglets */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'dashboard', label: 'Dashboard', icon: '📊' },
                  { id: 'validations', label: 'Validations', icon: '🔍' },
                  { id: 'vulnerabilities', label: 'Vulnérabilités', icon: '⚠️' },
                  { id: 'policies', label: 'Politiques', icon: '📋' },
                  { id: 'reports', label: 'Rapports', icon: '📈' },
                  { id: 'alerts', label: 'Alertes', icon: '🚨' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                  >
                    <span>{tab.icon}</span>
                    <span>{tab.label}</span>
                    {tab.id === 'alerts' && securityAlerts.filter(a => !a.acknowledged).length > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                        {securityAlerts.filter(a => !a.acknowledged).length}
                      </span>
                    )}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Contenu des onglets */}
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Statistiques principales */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm">📊</span>
                        </div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Total Validations
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {dashboardStats.totalValidations}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm">⏳</span>
                        </div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            En Attente
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {dashboardStats.pendingValidations}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm">⚠️</span>
                        </div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Vulnérabilités Critiques
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {dashboardStats.criticalVulnerabilities}
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                          <span className="text-white text-sm">✅</span>
                        </div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 truncate">
                            Taux de Conformité
                          </dt>
                          <dd className="text-lg font-medium text-gray-900">
                            {dashboardStats.complianceRate}%
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Graphiques et métriques */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Distribution des Vulnérabilités
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Critiques</span>
                      <span className="text-sm font-medium text-red-600">
                        {dashboardStats.criticalVulnerabilities}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Élevées</span>
                      <span className="text-sm font-medium text-orange-600">
                        {dashboardStats.highVulnerabilities}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Moyennes</span>
                      <span className="text-sm font-medium text-yellow-600">
                        {dashboardStats.mediumVulnerabilities}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Faibles</span>
                      <span className="text-sm font-medium text-blue-600">
                        {dashboardStats.lowVulnerabilities}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Statut des Validations
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Terminées</span>
                      <span className="text-sm font-medium text-green-600">
                        {dashboardStats.completedValidations}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">En cours</span>
                      <span className="text-sm font-medium text-yellow-600">
                        {dashboardStats.pendingValidations}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Bloquées</span>
                      <span className="text-sm font-medium text-red-600">
                        {dashboardStats.blockedValidations}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Onglet Validations */}
          {activeTab === 'validations' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  Validations de Sécurité
                </h3>
                <button
                  onClick={() => {
                    // Ouvrir modal pour nouvelle validation
                    console.log('Nouvelle validation');
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Nouvelle Validation
                </button>
              </div>

              {/* Filtres */}
              <div className="bg-white shadow rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Statut</label>
                    <select
                      value={filter.status || ''}
                      onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value as any }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm text-sm"
                    >
                      <option value="">Tous</option>
                      <option value="pending">En attente</option>
                      <option value="scanning">En cours</option>
                      <option value="completed">Terminé</option>
                      <option value="blocked">Bloqué</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Priorité</label>
                    <select
                      value={filter.priority || ''}
                      onChange={(e) => setFilter(prev => ({ ...prev, priority: e.target.value as any }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm text-sm"
                    >
                      <option value="">Toutes</option>
                      <option value="critical">Critique</option>
                      <option value="high">Élevée</option>
                      <option value="medium">Moyenne</option>
                      <option value="low">Faible</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Type</label>
                    <select
                      value={filter.type || ''}
                      onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value as any }))}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm text-sm"
                    >
                      <option value="">Tous</option>
                      <option value="code">Code</option>
                      <option value="container">Conteneur</option>
                      <option value="environment">Environnement</option>
                      <option value="deployment">Déploiement</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Recherche</label>
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="ID, agent, conteneur..."
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Liste des validations */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Validation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Priorité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredValidations().map((validation) => (
                      <tr key={validation.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {validation.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {validation.requestId}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            validation.status === 'completed' ? 'bg-green-100 text-green-800' :
                            validation.status === 'scanning' ? 'bg-yellow-100 text-yellow-800' :
                            validation.status === 'blocked' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {validation.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {validation.approved ? '✅' : '❌'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`font-medium ${
                            validation.score >= 80 ? 'text-green-600' :
                            validation.score >= 60 ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {validation.score}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {validation.timestamp.toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Onglet Vulnérabilités */}
          {activeTab === 'vulnerabilities' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  Vulnérabilités Détectées
                </h3>
                <div className="flex space-x-2">
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                    Nouveau Scan
                  </button>
                  <button className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                    Exporter
                  </button>
                </div>
              </div>

              {/* Liste des vulnérabilités */}
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vulnérabilité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sévérité
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Localisation
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        CVSS
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredVulnerabilities().map((vuln) => (
                      <tr key={vuln.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{vuln.title}</div>
                            <div className="text-sm text-gray-500">{vuln.description}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            vuln.severity === 'critical' ? 'bg-red-100 text-red-800' :
                            vuln.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                            vuln.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {vuln.severity}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {vuln.type}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {vuln.location}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {vuln.cvss || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            vuln.status === 'fixed' ? 'bg-green-100 text-green-800' :
                            vuln.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {vuln.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Onglet Alertes */}
          {activeTab === 'alerts' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900">
                  Alertes de Sécurité
                </h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      securityAlerts.forEach(alert => {
                        if (!alert.acknowledged) {
                          acknowledgeAlert(alert.id);
                        }
                      });
                    }}
                    className="bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700"
                  >
                    Acquitter Tout
                  </button>
                </div>
              </div>

              {/* Liste des alertes */}
              <div className="space-y-4">
                {securityAlerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`bg-white shadow rounded-lg p-6 border-l-4 ${
                      alert.severity === 'critical' ? 'border-red-500' :
                      alert.severity === 'high' ? 'border-orange-500' :
                      alert.severity === 'medium' ? 'border-yellow-500' :
                      'border-blue-500'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <h4 className="text-lg font-medium text-gray-900">{alert.title}</h4>
                          <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                            alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                            alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {alert.severity}
                          </span>
                          {alert.acknowledged && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Acquittée
                            </span>
                          )}
                          {alert.resolved && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Résolue
                            </span>
                          )}
                        </div>
                        <p className="mt-2 text-sm text-gray-600">{alert.description}</p>
                        <p className="mt-1 text-xs text-gray-500">
                          {alert.timestamp.toLocaleString()}
                          {alert.validationId && ` • Validation: ${alert.validationId}`}
                        </p>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        {!alert.acknowledged && (
                          <button
                            onClick={() => acknowledgeAlert(alert.id)}
                            className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                          >
                            Acquitter
                          </button>
                        )}
                        {!alert.resolved && (
                          <button
                            onClick={() => resolveAlert(alert.id)}
                            className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                          >
                            Résoudre
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Autres onglets (Politiques, Rapports) - Implémentation simplifiée */}
          {activeTab === 'policies' && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Politiques de Sécurité</h3>
              <p className="text-gray-600">Configuration et gestion des politiques de sécurité.</p>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Rapports de Sécurité</h3>
              <p className="text-gray-600">Génération et consultation des rapports de sécurité.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SecurityValidationInterface;
