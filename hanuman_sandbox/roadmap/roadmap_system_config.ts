/**
 * Configuration Globale du Système de Roadmap Obligatoire
 * Centralise toutes les configurations pour un déploiement cohérent
 */

import { RoadmapGeneratorConfig } from './roadmap_generator';
import { ValidationConfig } from './roadmap_validator_agent';
import { TrackerConfig } from './roadmap_tracker';
import { GateKeeperConfig } from './deployment_gate_keeper';

// Configuration d'environnement
export type Environment = 'development' | 'staging' | 'production';

// Configuration globale du système
export interface RoadmapSystemConfig {
  environment: Environment;
  enableStrictMode: boolean;
  enableAuditLogging: boolean;
  enableNotifications: boolean;
  generator: RoadmapGeneratorConfig;
  validator: ValidationConfig;
  tracker: TrackerConfig;
  gateKeeper: GateKeeperConfig;
  integrations: IntegrationConfig;
  security: SecurityConfig;
  monitoring: MonitoringConfig;
}

export interface IntegrationConfig {
  enableQAIntegration: boolean;
  enableSecurityIntegration: boolean;
  enableIDEIntegration: boolean;
  enableCICD: boolean;
  webhookEndpoints: string[];
  notificationChannels: NotificationChannel[];
}

export interface NotificationChannel {
  type: 'email' | 'slack' | 'teams' | 'webhook';
  endpoint: string;
  events: string[];
  enabled: boolean;
}

export interface SecurityConfig {
  enableEncryption: boolean;
  enableAuditTrail: boolean;
  enableRoleBasedAccess: boolean;
  sessionTimeout: number;
  maxFailedAttempts: number;
  requiredRoles: {
    roadmapCreation: string[];
    roadmapApproval: string[];
    deploymentOverride: string[];
    systemAdmin: string[];
  };
}

export interface MonitoringConfig {
  enableMetrics: boolean;
  enableAlerts: boolean;
  metricsRetentionDays: number;
  alertThresholds: {
    roadmapCompletionRate: number;
    validationFailureRate: number;
    deploymentBlockRate: number;
    overrideRate: number;
  };
  dashboardRefreshInterval: number;
}

// Configurations prédéfinies par environnement
export const DEVELOPMENT_CONFIG: RoadmapSystemConfig = {
  environment: 'development',
  enableStrictMode: false,
  enableAuditLogging: true,
  enableNotifications: false,
  generator: {
    defaultCreator: 'dev-system',
    defaultRiskOwner: 'dev-lead',
    enableAutoApproval: true,
    requireStakeholderApproval: false,
    maxProjectDuration: 30,
    estimationBuffer: 1.5
  },
  validator: {
    enableAutomaticValidation: true,
    requireStakeholderApproval: false,
    blockDeploymentOnErrors: false,
    allowWarningOverride: true,
    validationTimeout: 10000,
    requiredApprovers: ['dev-lead'],
    criticalValidationRules: ['roadmap_completeness']
  },
  tracker: {
    enableRealTimeTracking: true,
    alertThresholds: {
      scheduleDelayDays: 5,
      velocityDropPercent: 30,
      qualityScoreMin: 70,
      blockerResolutionHours: 48
    },
    updateInterval: 10000,
    enablePredictiveAnalytics: false,
    enableAutomaticAlerts: false
  },
  gateKeeper: {
    enableStrictMode: false,
    allowEmergencyOverride: true,
    emergencyOverrideRoles: ['dev-lead', 'senior-dev'],
    requireRoadmapForProduction: false,
    requireValidationForStaging: false,
    requireProgressTracking: false,
    autoCreateRoadmapForMissingProjects: true,
    notificationChannels: ['console']
  },
  integrations: {
    enableQAIntegration: true,
    enableSecurityIntegration: false,
    enableIDEIntegration: true,
    enableCICD: false,
    webhookEndpoints: [],
    notificationChannels: []
  },
  security: {
    enableEncryption: false,
    enableAuditTrail: true,
    enableRoleBasedAccess: false,
    sessionTimeout: 3600,
    maxFailedAttempts: 10,
    requiredRoles: {
      roadmapCreation: ['developer'],
      roadmapApproval: ['dev-lead'],
      deploymentOverride: ['dev-lead'],
      systemAdmin: ['admin']
    }
  },
  monitoring: {
    enableMetrics: true,
    enableAlerts: false,
    metricsRetentionDays: 30,
    alertThresholds: {
      roadmapCompletionRate: 80,
      validationFailureRate: 20,
      deploymentBlockRate: 10,
      overrideRate: 5
    },
    dashboardRefreshInterval: 30000
  }
};

export const STAGING_CONFIG: RoadmapSystemConfig = {
  environment: 'staging',
  enableStrictMode: true,
  enableAuditLogging: true,
  enableNotifications: true,
  generator: {
    defaultCreator: 'staging-system',
    defaultRiskOwner: 'project-manager',
    enableAutoApproval: false,
    requireStakeholderApproval: true,
    maxProjectDuration: 60,
    estimationBuffer: 1.3
  },
  validator: {
    enableAutomaticValidation: true,
    requireStakeholderApproval: true,
    blockDeploymentOnErrors: true,
    allowWarningOverride: false,
    validationTimeout: 20000,
    requiredApprovers: ['project-manager', 'tech-lead'],
    criticalValidationRules: ['roadmap_completeness', 'security_requirements', 'task_completeness']
  },
  tracker: {
    enableRealTimeTracking: true,
    alertThresholds: {
      scheduleDelayDays: 3,
      velocityDropPercent: 25,
      qualityScoreMin: 80,
      blockerResolutionHours: 24
    },
    updateInterval: 5000,
    enablePredictiveAnalytics: true,
    enableAutomaticAlerts: true
  },
  gateKeeper: {
    enableStrictMode: true,
    allowEmergencyOverride: true,
    emergencyOverrideRoles: ['cto', 'security-officer'],
    requireRoadmapForProduction: true,
    requireValidationForStaging: true,
    requireProgressTracking: true,
    autoCreateRoadmapForMissingProjects: false,
    notificationChannels: ['email', 'slack']
  },
  integrations: {
    enableQAIntegration: true,
    enableSecurityIntegration: true,
    enableIDEIntegration: true,
    enableCICD: true,
    webhookEndpoints: ['http://staging-ci.internal/webhook'],
    notificationChannels: [
      {
        type: 'slack',
        endpoint: 'https://hooks.slack.com/staging',
        events: ['roadmap_created', 'validation_failed', 'deployment_blocked'],
        enabled: true
      }
    ]
  },
  security: {
    enableEncryption: true,
    enableAuditTrail: true,
    enableRoleBasedAccess: true,
    sessionTimeout: 1800,
    maxFailedAttempts: 5,
    requiredRoles: {
      roadmapCreation: ['developer', 'project-manager'],
      roadmapApproval: ['project-manager', 'tech-lead'],
      deploymentOverride: ['cto', 'security-officer'],
      systemAdmin: ['admin', 'devops']
    }
  },
  monitoring: {
    enableMetrics: true,
    enableAlerts: true,
    metricsRetentionDays: 90,
    alertThresholds: {
      roadmapCompletionRate: 85,
      validationFailureRate: 15,
      deploymentBlockRate: 8,
      overrideRate: 3
    },
    dashboardRefreshInterval: 15000
  }
};

export const PRODUCTION_CONFIG: RoadmapSystemConfig = {
  environment: 'production',
  enableStrictMode: true,
  enableAuditLogging: true,
  enableNotifications: true,
  generator: {
    defaultCreator: 'production-system',
    defaultRiskOwner: 'project-manager',
    enableAutoApproval: false,
    requireStakeholderApproval: true,
    maxProjectDuration: 90,
    estimationBuffer: 1.2
  },
  validator: {
    enableAutomaticValidation: true,
    requireStakeholderApproval: true,
    blockDeploymentOnErrors: true,
    allowWarningOverride: false,
    validationTimeout: 30000,
    requiredApprovers: ['project-manager', 'tech-lead', 'security-officer'],
    criticalValidationRules: [
      'roadmap_completeness',
      'security_requirements',
      'task_completeness',
      'deliverable_validation',
      'risk_assessment'
    ]
  },
  tracker: {
    enableRealTimeTracking: true,
    alertThresholds: {
      scheduleDelayDays: 2,
      velocityDropPercent: 20,
      qualityScoreMin: 85,
      blockerResolutionHours: 12
    },
    updateInterval: 3000,
    enablePredictiveAnalytics: true,
    enableAutomaticAlerts: true
  },
  gateKeeper: {
    enableStrictMode: true,
    allowEmergencyOverride: true,
    emergencyOverrideRoles: ['cto'],
    requireRoadmapForProduction: true,
    requireValidationForStaging: true,
    requireProgressTracking: true,
    autoCreateRoadmapForMissingProjects: false,
    notificationChannels: ['email', 'slack', 'teams']
  },
  integrations: {
    enableQAIntegration: true,
    enableSecurityIntegration: true,
    enableIDEIntegration: true,
    enableCICD: true,
    webhookEndpoints: [
      'http://prod-ci.internal/webhook',
      'http://monitoring.internal/webhook'
    ],
    notificationChannels: [
      {
        type: 'slack',
        endpoint: 'https://hooks.slack.com/production',
        events: ['roadmap_created', 'validation_failed', 'deployment_blocked', 'override_used'],
        enabled: true
      },
      {
        type: 'email',
        endpoint: '<EMAIL>',
        events: ['deployment_blocked', 'override_used', 'security_issue'],
        enabled: true
      }
    ]
  },
  security: {
    enableEncryption: true,
    enableAuditTrail: true,
    enableRoleBasedAccess: true,
    sessionTimeout: 900,
    maxFailedAttempts: 3,
    requiredRoles: {
      roadmapCreation: ['senior-developer', 'project-manager'],
      roadmapApproval: ['project-manager', 'tech-lead', 'security-officer'],
      deploymentOverride: ['cto'],
      systemAdmin: ['admin']
    }
  },
  monitoring: {
    enableMetrics: true,
    enableAlerts: true,
    metricsRetentionDays: 365,
    alertThresholds: {
      roadmapCompletionRate: 90,
      validationFailureRate: 10,
      deploymentBlockRate: 5,
      overrideRate: 1
    },
    dashboardRefreshInterval: 10000
  }
};

// Factory pour créer la configuration selon l'environnement
export class RoadmapSystemConfigFactory {
  static createConfig(environment: Environment): RoadmapSystemConfig {
    switch (environment) {
      case 'development':
        return DEVELOPMENT_CONFIG;
      case 'staging':
        return STAGING_CONFIG;
      case 'production':
        return PRODUCTION_CONFIG;
      default:
        throw new Error(`Environnement non supporté: ${environment}`);
    }
  }

  static createCustomConfig(
    baseEnvironment: Environment,
    overrides: Partial<RoadmapSystemConfig>
  ): RoadmapSystemConfig {
    const baseConfig = this.createConfig(baseEnvironment);
    return this.mergeConfigs(baseConfig, overrides);
  }

  private static mergeConfigs(
    base: RoadmapSystemConfig,
    overrides: Partial<RoadmapSystemConfig>
  ): RoadmapSystemConfig {
    return {
      ...base,
      ...overrides,
      generator: { ...base.generator, ...overrides.generator },
      validator: { ...base.validator, ...overrides.validator },
      tracker: { ...base.tracker, ...overrides.tracker },
      gateKeeper: { ...base.gateKeeper, ...overrides.gateKeeper },
      integrations: { ...base.integrations, ...overrides.integrations },
      security: { ...base.security, ...overrides.security },
      monitoring: { ...base.monitoring, ...overrides.monitoring }
    };
  }

  static validateConfig(config: RoadmapSystemConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validation de base
    if (!config.environment) {
      errors.push('Environnement requis');
    }

    // Validation du générateur
    if (config.generator.maxProjectDuration <= 0) {
      errors.push('Durée maximale de projet doit être positive');
    }

    if (config.generator.estimationBuffer < 1) {
      errors.push('Buffer d\'estimation doit être >= 1');
    }

    // Validation du validateur
    if (config.validator.validationTimeout <= 0) {
      errors.push('Timeout de validation doit être positif');
    }

    if (config.validator.requiredApprovers.length === 0 && config.validator.requireStakeholderApproval) {
      errors.push('Approbateurs requis si approbation obligatoire');
    }

    // Validation du tracker
    if (config.tracker.updateInterval <= 0) {
      errors.push('Intervalle de mise à jour doit être positif');
    }

    // Validation de la sécurité
    if (config.security.sessionTimeout <= 0) {
      errors.push('Timeout de session doit être positif');
    }

    if (config.security.maxFailedAttempts <= 0) {
      errors.push('Nombre max de tentatives doit être positif');
    }

    // Validation du monitoring
    if (config.monitoring.metricsRetentionDays <= 0) {
      errors.push('Rétention des métriques doit être positive');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Configuration par défaut (development)
export const DEFAULT_CONFIG = DEVELOPMENT_CONFIG;

// Export des configurations
export {
  DEVELOPMENT_CONFIG as DEV_CONFIG,
  STAGING_CONFIG,
  PRODUCTION_CONFIG as PROD_CONFIG
};
